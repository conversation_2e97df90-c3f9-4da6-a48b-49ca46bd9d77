/* Event Detail Page Styles */

.container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.header {
  margin-bottom: 2rem;
}

.backButton {
  background: none;
  border: none;
  color: #4ECDC4;
  font-size: 1rem;
  cursor: pointer;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.backButton:hover {
  color: #44A08D;
}

.eventHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.eventTitle {
  flex: 1;
}

.eventTitle h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.statusBadge {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

.statusBadge.upcoming {
  background: #3498db;
}

.statusBadge.active {
  background: #2ecc71;
}

.statusBadge.completed {
  background: #95a5a6;
}

.statusBadge.cancelled {
  background: #e74c3c;
}

.generateButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
}

.generateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

/* Financial Tracking Form Styles */
.sectionHeader {
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.sectionHeader h3 {
  margin: 0;
  color: #374151;
  font-size: 1.2rem;
  font-weight: 600;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.checkboxLabel:hover {
  border-color: #4ECDC4;
  background: #f0fdfa;
}

.checkbox {
  width: 1.25rem;
  height: 1.25rem;
  accent-color: #4ECDC4;
  cursor: pointer;
}

.checkboxText {
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  user-select: none;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
  }
}

.buttonIcon {
  font-size: 1.2rem;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid #e1e8ed;
  margin-bottom: 2rem;
  gap: 0;
}

.tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab:hover {
  color: #4ECDC4;
  background: rgba(78, 205, 196, 0.05);
}

.tab.active {
  color: #4ECDC4;
  border-bottom-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.1);
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

/* Overview Tab */
.overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.eventDetails {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.eventDetails h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

.detailsGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 1rem;
  padding: 0.8rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail:last-child {
  border-bottom: none;
}

.detailLabel {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.detailValue {
  color: #2c3e50;
  font-size: 0.9rem;
}

.description {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #f0f0f0;
}

.description h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
}

.description p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Quick Stats */
.quickStats {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quickStats h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

.statsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.statCard {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* QR Codes Grid */
.qrCodesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.qrCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e1e8ed;
}

.qrCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.qrHeader {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qrHeader h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.qrStatus {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qrStatus.active {
  background: #d4edda;
  color: #155724;
}

.qrStatus.inactive {
  background: #f8d7da;
  color: #721c24;
}

.qrCode {
  padding: 0 1.5rem;
  text-align: center;
}

.qrCodeText {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.8rem;
  border-radius: 6px;
  display: inline-block;
  word-break: break-all;
}

.qrStats {
  padding: 1rem 1.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  border-top: 1px solid #f0f0f0;
}

.qrStat {
  text-align: center;
}

.qrStatValue {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 0.2rem;
}

.qrStatLabel {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qrActions {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  gap: 0.5rem;
}

.qrActionButton {
  flex: 1;
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 0.8rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.qrActionButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Analytics */
.analytics {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.analyticsHeader {
  margin-bottom: 2rem;
}

.analyticsHeader h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.analyticsHeader p {
  color: #666;
  margin: 0;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-4px);
}

.metricValue {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metricLabel {
  font-size: 0.9rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.selectedQRAnalytics {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #4ECDC4;
}

.selectedQRAnalytics h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
}

.qrMetrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.qrMetric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: white;
  border-radius: 6px;
}

.qrMetricLabel {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.qrMetricValue {
  font-weight: 700;
  color: #4ECDC4;
  font-size: 1rem;
}

/* Loading and Empty States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.emptyState p {
  color: #666;
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.errorContainer h1 {
  font-size: 2rem;
  color: #e74c3c;
  margin: 0 0 1rem 0;
}

.errorContainer p {
  color: #666;
  margin: 0 0 2rem 0;
}

.errorContainer button {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.errorContainer button:hover {
  background: #44A08D;
  transform: translateY(-2px);
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.input {
  padding: 0.8rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.input:focus {
  outline: none;
  border-color: #4ECDC4;
}

.helpText {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e8ed;
}

.cancelButton {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e8ed;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

.submitButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .eventHeader {
    flex-direction: column;
    gap: 1rem;
  }

  .eventTitle h1 {
    font-size: 2rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .generateButton {
    align-self: stretch;
    justify-content: center;
  }

  .tabs {
    overflow-x: auto;
    white-space: nowrap;
  }

  .tab {
    flex-shrink: 0;
  }

  .overview {
    grid-template-columns: 1fr;
  }

  .qrCodesGrid {
    grid-template-columns: 1fr;
  }

  .metricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .metricCard {
    padding: 1.5rem;
  }

  .metricValue {
    font-size: 2rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .qrStats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .qrActions {
    flex-direction: column;
  }

  .formActions {
    flex-direction: column;
  }
}
