import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  // Only allow DELETE requests
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request using our robust auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error('Delete user API: Authentication failed:', error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    console.log('Delete user API: Authentication successful. User:', user?.email, 'Role:', role)
    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can delete users.' })
    }

    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get user ID from request body
    const { userId } = req.body

    // Validate required fields
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Prevent self-deletion
    if (userId === user.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' })
    }

    // Check if user exists and get their info
    const { data: userToDelete, error: fetchError } = await adminClient
      .from('user_profiles')
      .select('name, id')
      .eq('id', userId)
      .single()

    if (fetchError || !userToDelete) {
      console.error('Error fetching user to delete:', fetchError)
      return res.status(404).json({ error: 'User not found' })
    }

    // Get user role to check if they're trying to delete another admin/dev
    const { data: userRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', userId)
      .single()

    if (roleError) {
      console.error('Error fetching user role:', roleError)
      return res.status(500).json({ error: 'Failed to fetch user role' })
    }

    // Prevent deletion of dev users unless current user is also dev
    if (userRole?.role === 'dev' && role !== 'dev') {
      return res.status(403).json({ error: 'Only developers can delete other developer accounts' })
    }

    // Log the deletion activity before deleting
    try {
      await adminClient
        .from('user_activity_log')
        .insert([
          {
            user_id: userId,
            activity_type: 'user_deleted',
            activity_description: `User account deleted by ${user.email}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          }
        ])
    } catch (activityError) {
      console.error('Error logging user deletion activity:', activityError)
      // Continue anyway - activity logging is not critical
    }

    // Delete user from Supabase Auth (this will cascade delete related records due to foreign key constraints)
    const { error: deleteError } = await adminClient.auth.admin.deleteUser(userId)

    if (deleteError) {
      console.error('Error deleting user from auth:', deleteError)
      return res.status(500).json({ error: 'Failed to delete user account' })
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User ${userToDelete.name || 'Unknown'} deleted successfully`
    })

  } catch (error) {
    console.error('Unexpected error deleting user:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
